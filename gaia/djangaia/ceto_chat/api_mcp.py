"""
MCP API abstraction for ceto_chat Django app.
Level 0033: Separate MCP API concerns
Level 0034: Self-contained MCP client implementation

Simple abstraction layer for MCP server communication.
Handles connection management and tool listing without external dependencies.
"""

import asyncio
import json
import logging
from typing import Dict, Any
from django.conf import settings
import aiohttp

logger = logging.getLogger(__name__)


class MCPService:
    """
    Simple MCP service abstraction.
    Level 0034: Self-contained implementation without external dependencies.
    """

    def __init__(self):
        self.server_url = getattr(settings, 'MCP_SERVER_URL', 'http://0.0.0.0:9000/mcp')
        self.timeout = getattr(settings, 'MCP_SERVER_TIMEOUT', 30)

    async def _make_mcp_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make a direct HTTP request to the MCP server.
        Level 0034: Simple HTTP-based MCP communication.
        """
        request_data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(
                    self.server_url,
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result
                    else:
                        logger.error(f"MCP server returned status {response.status}")
                        return {"error": f"HTTP {response.status}"}

        except Exception as e:
            logger.error(f"Error making MCP request: {e}")
            return {"error": str(e)}
    
    def get_tools(self) -> Dict[str, Any]:
        """
        Get available MCP tools.
        Level 0034: Direct HTTP-based tool listing.
        """
        # Run async operation in new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(self._make_mcp_request("tools/list"))

            if "error" in result:
                return {
                    'success': False,
                    'error': result["error"],
                    'tools': [],
                    'server_url': self.server_url,
                    'tool_count': 0
                }

            # Extract tools from MCP response
            tools_data = result.get("result", {}).get("tools", [])

            # Format tools for frontend
            formatted_tools = []
            for tool in tools_data:
                formatted_tools.append({
                    'name': tool.get('name', 'unknown'),
                    'description': tool.get('description', 'No description'),
                    'input_schema': tool.get('inputSchema', {})
                })

            return {
                'success': True,
                'tools': formatted_tools,
                'server_url': self.server_url,
                'tool_count': len(formatted_tools)
            }

        except Exception as e:
            logger.error(f"Error in get_tools: {e}")
            return {
                'success': False,
                'error': f'MCP request error: {str(e)}',
                'tools': [],
                'server_url': self.server_url,
                'tool_count': 0
            }
        finally:
            loop.close()

    async def _call_tool_async(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async method to call an MCP tool.
        Level 0034: Direct HTTP-based tool calling.
        """
        params = {
            "name": tool_name,
            "arguments": tool_args
        }

        logger.info(f"Calling MCP tool '{tool_name}' with args: {tool_args}")
        result = await self._make_mcp_request("tools/call", params)

        if "error" in result:
            return {
                'success': False,
                'error': result["error"],
                'result': None,
                'tool_name': tool_name,
                'tool_args': tool_args
            }

        # Extract result from MCP response
        tool_result = result.get("result", {})

        if tool_result.get("isError", False):
            return {
                'success': False,
                'error': tool_result.get("content", [{}])[0].get("text", "Tool execution failed"),
                'result': None,
                'tool_name': tool_name,
                'tool_args': tool_args
            }

        # Extract content from successful result
        content = tool_result.get("content", [])
        if content and isinstance(content, list) and len(content) > 0:
            result_text = content[0].get("text", "")
        else:
            result_text = str(tool_result)

        return {
            'success': True,
            'result': result_text,
            'tool_name': tool_name,
            'tool_args': tool_args
        }

    def call_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Synchronous wrapper for calling MCP tools.
        Level 0034: Tool calling functionality
        """
        # Run async operation in new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(self._call_tool_async(tool_name, tool_args))
            return result
        except Exception as e:
            logger.error(f"Error in call_tool: {e}")
            return {
                'success': False,
                'error': f'Tool call failed: {str(e)}',
                'result': None,
                'tool_name': tool_name,
                'tool_args': tool_args
            }
        finally:
            loop.close()


# Singleton instance for the app
mcp_service = MCPService()
