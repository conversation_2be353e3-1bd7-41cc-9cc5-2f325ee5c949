"""
MCP API abstraction for ceto_chat Django app.
Level 0033: Separate MCP API concerns

Simple abstraction layer for MCP server communication.
Handles connection management and tool listing.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from django.conf import settings

# Import MCP client library
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False

logger = logging.getLogger(__name__)


class MCPService:
    """
    Simple MCP service abstraction.
    Handles single MCP server connection as specified in level 0033.
    """
    
    def __init__(self):
        self.server_url = getattr(settings, 'MCP_SERVER_URL', 'http://0.0.0.0:9000/mcp')
        self.timeout = getattr(settings, 'MCP_SERVER_TIMEOUT', 30)
    
    def _debug_callback(self, level: str, msg: str, data: Any = None) -> None:
        """Debug callback for MCP client."""
        logger.info(f"MCP Debug [{level}]: {msg}")
    
    async def _get_client_and_tools(self) -> tuple[Optional[MCPClientLib], List[Dict[str, Any]]]:
        """
        Create MCP client, connect, and get tools.
        Returns (client, tools) tuple.
        """
        if not MCP_AVAILABLE:
            return None, []
        
        client = MCPClientLib(debug_callback=self._debug_callback)
        
        try:
            # Connect to server
            success = await client.connect_to_server(self.server_url)
            if not success:
                logger.error(f"Failed to connect to MCP server at {self.server_url}")
                return None, []
            
            # Get tools
            tools = client.available_tools or []
            return client, tools
            
        except Exception as e:
            logger.error(f"Error connecting to MCP server: {e}")
            await client.cleanup()
            return None, []
    
    def get_tools(self) -> Dict[str, Any]:
        """
        Get available MCP tools.
        Returns dict with success status, tools list, and metadata.
        """
        if not MCP_AVAILABLE:
            return {
                'success': False,
                'error': 'MCP client library not available',
                'tools': [],
                'server_url': self.server_url,
                'tool_count': 0
            }
        
        # Run async operation in new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            client, tools = loop.run_until_complete(self._get_client_and_tools())
            
            if client is None:
                return {
                    'success': False,
                    'error': f'Failed to connect to MCP server at {self.server_url}',
                    'tools': [],
                    'server_url': self.server_url,
                    'tool_count': 0
                }
            
            # Format tools for frontend
            formatted_tools = []
            for tool in tools:
                formatted_tools.append({
                    'name': tool.get('name', 'unknown'),
                    'description': tool.get('description', 'No description'),
                    'input_schema': tool.get('input_schema', {})
                })
            
            # Cleanup
            loop.run_until_complete(client.cleanup())
            
            return {
                'success': True,
                'tools': formatted_tools,
                'server_url': self.server_url,
                'tool_count': len(formatted_tools)
            }
            
        except Exception as e:
            logger.error(f"Error in get_tools: {e}")
            return {
                'success': False,
                'error': f'MCP client error: {str(e)}',
                'tools': [],
                'server_url': self.server_url,
                'tool_count': 0
            }
        finally:
            loop.close()

    async def _call_tool_async(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async method to call an MCP tool.
        Returns result dictionary with success/error status.
        """
        if not MCP_AVAILABLE:
            return {
                'success': False,
                'error': 'MCP client library not available',
                'result': None
            }

        client = MCPClientLib(debug_callback=self._debug_callback)

        try:
            # Connect to server
            success = await client.connect_to_server(self.server_url)
            if not success:
                logger.error(f"Failed to connect to MCP server at {self.server_url}")
                return {
                    'success': False,
                    'error': f'Failed to connect to MCP server at {self.server_url}',
                    'result': None
                }

            # Call the tool
            logger.info(f"Calling MCP tool '{tool_name}' with args: {tool_args}")
            result = await client.call_tool(tool_name, tool_args)

            if result.success:
                return {
                    'success': True,
                    'result': result.content,
                    'tool_name': tool_name,
                    'tool_args': tool_args
                }
            else:
                return {
                    'success': False,
                    'error': result.error,
                    'result': None,
                    'tool_name': tool_name,
                    'tool_args': tool_args
                }

        except Exception as e:
            logger.error(f"Error calling MCP tool '{tool_name}': {e}")
            return {
                'success': False,
                'error': f'MCP tool call error: {str(e)}',
                'result': None,
                'tool_name': tool_name,
                'tool_args': tool_args
            }
        finally:
            await client.cleanup()

    def call_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Synchronous wrapper for calling MCP tools.
        Level 0034: Tool calling functionality
        """
        # Run async operation in new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(self._call_tool_async(tool_name, tool_args))
            return result
        except Exception as e:
            logger.error(f"Error in call_tool: {e}")
            return {
                'success': False,
                'error': f'Tool call failed: {str(e)}',
                'result': None,
                'tool_name': tool_name,
                'tool_args': tool_args
            }
        finally:
            loop.close()


# Singleton instance for the app
mcp_service = MCPService()
