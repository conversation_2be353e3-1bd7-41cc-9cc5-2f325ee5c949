from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json
import logging

# Import MCP service abstraction
from .api_mcp import mcp_service

logger = logging.getLogger(__name__)


def ceto_chat_view(request):
    """
    Renders the Ceto Chat base template.
    """
    return render(request, 'ceto_chat/ceto_chat_base.html')


@csrf_exempt
@require_http_methods(["GET"])
def list_mcp_tools(request):
    """
    List available MCP tools from the running server.
    Level 0033: Use MCP service abstraction
    """
    result = mcp_service.get_tools()

    if result['success']:
        return JsonResponse(result)
    else:
        return JsonResponse(result, status=500)
