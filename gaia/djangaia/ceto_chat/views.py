from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json
import logging

# Import MCP service abstraction
from .api_mcp import mcp_service

logger = logging.getLogger(__name__)


def ceto_chat_view(request):
    """
    Renders the Ceto Chat base template.
    """
    return render(request, 'ceto_chat/ceto_chat_base.html')


@csrf_exempt
@require_http_methods(["GET"])
def list_mcp_tools(request):
    """
    List available MCP tools from the running server.
    Level 0033: Use MCP service abstraction
    """
    result = mcp_service.get_tools()

    if result['success']:
        return JsonResponse(result)
    else:
        return JsonResponse(result, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def call_mcp_tool(request):
    """
    Call an MCP tool with provided arguments.
    Level 0034: Frontend-to-backend MCP tool calling
    """
    try:
        data = json.loads(request.body)
        tool_name = data.get('tool_name')
        tool_args = data.get('tool_args', {})

        if not tool_name:
            return JsonResponse({
                'success': False,
                'error': 'tool_name is required'
            }, status=400)

        logger.info(f"Calling MCP tool: {tool_name} with args: {tool_args}")

        # Call the tool via MCP service
        result = mcp_service.call_tool(tool_name, tool_args)

        logger.info(f"MCP tool result: {result}")

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON in request body'
        }, status=400)
    except Exception as e:
        logger.error(f"Error calling MCP tool: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
