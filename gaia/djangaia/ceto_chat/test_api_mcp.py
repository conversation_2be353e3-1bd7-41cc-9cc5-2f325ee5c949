"""
Test for MCP API abstraction.
Level 0033: Validate separated MCP API concerns
"""

import unittest
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.conf import settings

from .api_mcp import MCPService, mcp_service


class TestMCPService(TestCase):
    """Test the MCP service abstraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = MCPService()
    
    def test_service_initialization(self):
        """Test that service initializes with correct settings."""
        self.assertEqual(self.service.server_url, getattr(settings, 'MCP_SERVER_URL', 'http://0.0.0.0:9000/mcp'))
        self.assertEqual(self.service.timeout, getattr(settings, 'MCP_SERVER_TIMEOUT', 30))
    
    @patch('gaia.djangaia.ceto_chat.api_mcp.MCP_AVAILABLE', False)
    def test_get_tools_when_mcp_unavailable(self):
        """Test get_tools when MCP library is not available."""
        result = self.service.get_tools()
        
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'MCP client library not available')
        self.assertEqual(result['tools'], [])
        self.assertEqual(result['tool_count'], 0)
    
    @patch('gaia.djangaia.ceto_chat.api_mcp.MCP_AVAILABLE', True)
    @patch('gaia.djangaia.ceto_chat.api_mcp.MCPClientLib')
    @patch('asyncio.new_event_loop')
    def test_get_tools_connection_failure(self, mock_loop_class, mock_client_class):
        """Test get_tools when connection to MCP server fails."""
        # Mock the event loop
        mock_loop = MagicMock()
        mock_loop_class.return_value = mock_loop
        
        # Mock the client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client
        
        # Mock connection failure
        mock_loop.run_until_complete.return_value = (None, [])
        
        result = self.service.get_tools()
        
        self.assertFalse(result['success'])
        self.assertIn('Failed to connect to MCP server', result['error'])
        self.assertEqual(result['tools'], [])
        self.assertEqual(result['tool_count'], 0)
    
    @patch('gaia.djangaia.ceto_chat.api_mcp.MCP_AVAILABLE', True)
    @patch('gaia.djangaia.ceto_chat.api_mcp.MCPClientLib')
    @patch('asyncio.new_event_loop')
    def test_get_tools_success(self, mock_loop_class, mock_client_class):
        """Test get_tools when connection succeeds."""
        # Mock the event loop
        mock_loop = MagicMock()
        mock_loop_class.return_value = mock_loop
        
        # Mock the client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client
        
        # Mock successful connection with tools
        mock_tools = [
            {
                'name': 'test_tool',
                'description': 'A test tool',
                'input_schema': {'type': 'object'}
            }
        ]
        mock_loop.run_until_complete.return_value = (mock_client, mock_tools)
        
        result = self.service.get_tools()
        
        self.assertTrue(result['success'])
        self.assertEqual(len(result['tools']), 1)
        self.assertEqual(result['tools'][0]['name'], 'test_tool')
        self.assertEqual(result['tool_count'], 1)
        self.assertEqual(result['server_url'], self.service.server_url)
    
    def test_singleton_instance(self):
        """Test that mcp_service is a singleton instance."""
        self.assertIsInstance(mcp_service, MCPService)


class TestMCPViews(TestCase):
    """Test the MCP views that use the service."""
    
    def test_list_mcp_tools_endpoint(self):
        """Test the list_mcp_tools endpoint."""
        response = self.client.get('/ceto_chat/api/mcp/tools/')
        
        # Should return JSON response
        self.assertEqual(response['Content-Type'], 'application/json')
        
        # Should have expected structure
        data = response.json()
        self.assertIn('success', data)
        self.assertIn('tools', data)
        self.assertIn('server_url', data)
        self.assertIn('tool_count', data)


if __name__ == '__main__':
    unittest.main()
