"""
MCP HTTP Client Library

This module provides a reusable client library for interacting with MCP servers via streamable HTTP.
It handles the core functionality of connecting to an MCP server, calling tools,
and processing queries with <PERSON>.
"""

import asyncio
from typing import Optional, List, Dict, Any, Callable, Union
from contextlib import AsyncExitStack
import logging

# Import ClientSession and types from mcp core
from mcp import ClientSession, types
# Import streamablehttp_client specifically for HTTP transport
from mcp.client.streamable_http import streamablehttp_client

# Import Anthropic conditionally - only needed for legacy mode
try:
    from anthropic import Anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    Anthropic = None

# Import LiteLLM conditionally
try:
    import litellm
    LITELLM_AVAILABLE = True
except ImportError:
    LITELLM_AVAILABLE = False
    litellm = None

# Configure logging
logger = logging.getLogger(__name__)

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:9000/mcp"  # Default URL for HTTP
# Import default model from centralized config
try:
    from gaia.gaia_llm.model_config import DEFAULT_ANTHROPIC_MODEL
    DEFAULT_MODEL = DEFAULT_ANTHROPIC_MODEL
except ImportError:
    DEFAULT_MODEL = "claude-3-5-sonnet-20241022"  # Fallback if import fails

class ToolCallResult:
    """Represents the result of a tool call, with additional metadata."""

    def __init__(self,
                 tool_name: str,
                 tool_input: Any,
                 tool_call_id: str,
                 success: bool = True,
                 content: Optional[str] = None,
                 error: Optional[str] = None,
                 execution_time: Optional[float] = None):
        self.tool_name = tool_name
        self.tool_input = tool_input
        self.tool_call_id = tool_call_id
        self.success = success
        self.content = content
        self.error = error
        self.execution_time = execution_time
        self.timestamp = None  # Can be set by the caller if needed

    def to_dict(self) -> Dict[str, Any]:
        """Convert to a dictionary for storage or serialization."""
        return {
            "tool_name": self.tool_name,
            "tool_input": self.tool_input,
            "tool_call_id": self.tool_call_id,
            "success": self.success,
            "content": self.content,
            "error": self.error,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp
        }

    def to_tool_result_block(self) -> Dict[str, Any]:
        """Convert to a tool result block for Claude API."""
        result = {
            "type": "tool_result",
            "tool_use_id": self.tool_call_id,
        }

        if not self.success:
            result["is_error"] = True
            result["content"] = self.error or f"Error executing tool {self.tool_name}."
        else:
            result["content"] = self.content or ""

        return result


class MCPClientLib:
    """Core client library for interacting with MCP servers via streamable HTTP."""

    def __init__(self,
                 anthropic_api_key: Optional[str] = None,
                 debug_callback: Optional[Callable] = None,
                 use_litellm: bool = True):
        """Initialize the MCP client library.

        Args:
            anthropic_api_key: Optional API key for Anthropic. If not provided,
                               it will be loaded from environment variables.
            debug_callback: Optional callback function for debug messages.
                            Function signature: callback(level: str, message: str, data: Any)
            use_litellm: Whether to use LiteLLM for unified model access (default: True)
        """
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.available_tools = []
        self.connected = False
        self.debug_callback = debug_callback
        self.use_litellm = use_litellm

        # Handle LLM client initialization
        try:
            import os

            if self.use_litellm:
                # Use LiteLLM for unified model access
                if not LITELLM_AVAILABLE:
                    logger.error("LiteLLM library not available. Install with: pip install litellm")
                    raise ImportError("LiteLLM library not available")

                self.anthropic = None  # Not needed when using LiteLLM
                logger.info("Using LiteLLM for unified model access")
            else:
                # Legacy Anthropic-only mode
                if not ANTHROPIC_AVAILABLE:
                    logger.error("Anthropic library not available. Install with: pip install anthropic")
                    raise ImportError("Anthropic library not available")

                # If API key is provided, use it
                if anthropic_api_key:
                    self.anthropic = Anthropic(api_key=anthropic_api_key)
                # Otherwise, try to get it from environment variables
                elif "ANTHROPIC_API_KEY" in os.environ:
                    self.anthropic = Anthropic(api_key=os.environ["ANTHROPIC_API_KEY"])
                else:
                    # Try to create without explicit key (may use ~/.anthropic/config.json)
                    self.anthropic = Anthropic()
                    # Test if it works
                    if not hasattr(self.anthropic, "api_key") or not self.anthropic.api_key:
                        # Use logger directly to avoid async issues during initialization
                        logger.warning("No Anthropic API key found. LLM functionality will not work.")
                        print("No Anthropic API key found. LLM functionality will not work.")
        except Exception as e:
            # Use logger directly to avoid async issues during initialization
            logger.error(f"Error initializing Anthropic client: {e}")
            print(f"Error initializing Anthropic client: {e}")
            self.anthropic = None

    async def _debug(self, level: str, message: str, data: Any = None):
        """Send debug information to the callback if provided."""
        if self.debug_callback:
            try:
                # Call the debug callback and await it if it's a coroutine
                result = self.debug_callback(level, message, data)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                # Don't let debug callback errors affect the main flow
                logger.error(f"Error in debug callback: {e}")

        # Also log using the standard logging module
        if level == "info":
            logger.info(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "error":
            logger.error(message)
        elif level == "debug":
            logger.debug(message)

    def _convert_tools_for_litellm(self, mcp_tools, model):
        """Convert MCP tools to the appropriate format based on the model provider."""
        # Determine provider from model name
        if model.startswith(('gpt-', 'o1-')) or 'openai' in model.lower():
            # OpenAI format
            converted_tools = []
            for tool in mcp_tools:
                openai_tool = {
                    "type": "function",
                    "function": {
                        "name": tool["name"],
                        "description": tool.get("description", ""),
                        "parameters": tool.get("inputSchema", {})
                    }
                }
                converted_tools.append(openai_tool)
            return converted_tools
        else:
            # Anthropic format (default) - return as-is since MCP tools are already in Anthropic format
            return mcp_tools

    def _normalize_response(self, response):
        """Normalize response format between LiteLLM and Anthropic."""
        if self.use_litellm:
            # LiteLLM response format - convert to Anthropic-like format
            choice = response.choices[0]
            message = choice.message

            # Create normalized content blocks
            content_blocks = []

            # Handle text content
            if message.content:
                content_blocks.append(type('ContentBlock', (), {
                    'type': 'text',
                    'text': message.content
                })())

            # Handle tool calls
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    import json
                    try:
                        arguments = json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                    except json.JSONDecodeError:
                        arguments = {}

                    content_blocks.append(type('ContentBlock', (), {
                        'type': 'tool_use',
                        'id': tool_call.id,
                        'name': tool_call.function.name,
                        'input': arguments
                    })())

            # Create normalized response object
            return type('Response', (), {
                'content': content_blocks
            })()
        else:
            # Anthropic response - return as-is
            return response

    async def connect_to_server(self, server_url: str = DEFAULT_SERVER_URL) -> bool:
        """Connect to an MCP server via streamable HTTP transport.

        Args:
            server_url: The HTTP URL of the server's streamable HTTP endpoint.

        Returns:
            bool: True if connection was successful, False otherwise.
        """
        await self._debug("info", f"Connecting to MCP server via streamable HTTP at: {server_url}")

        try:
            # Establish streamable HTTP transport connection using the provided URL
            await self._debug("info", f"🔍 HTTP TRANSPORT: Connecting to {server_url}")
            http_transport = await self.exit_stack.enter_async_context(streamablehttp_client(server_url))
            read_stream, write_stream, _ = http_transport  # Ignore the session ID callback
            await self._debug("info", f"🔍 HTTP TRANSPORT: Connection established, streams: {type(read_stream)}, {type(write_stream)}")
            await self._debug("debug", "Streamable HTTP transport connection established successfully.")

            # Create and initialize the MCP ClientSession using the HTTP streams
            self.session = await self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
            await self._debug("info", "Initializing MCP session (performing handshake)...")
            await self.session.initialize()
            await self._debug("info", "MCP session initialized successfully.")

            # List available tools from the connected server
            await self._debug("info", "🔍 DEBUG: About to call list_tools()")
            try:
                response = await self.session.list_tools()
                await self._debug("info", f"🔍 DEBUG: list_tools() succeeded, response type: {type(response)}")
                tools = response.tools
                await self._debug("info", f"🔍 DEBUG: Got tools from response: {type(tools)}")
            except Exception as e:
                await self._debug("error", f"🔍 DEBUG: list_tools() failed: {e}")
                await self._debug("error", f"🔍 DEBUG: Exception type: {type(e)}")
                import traceback
                await self._debug("error", f"🔍 DEBUG: Traceback: {traceback.format_exc()}")
                raise

            # DEBUG: Log what we actually received
            await self._debug("info", f"🔍 DEBUG: response type: {type(response)}")
            await self._debug("info", f"🔍 DEBUG: tools type: {type(tools)}")
            await self._debug("info", f"🔍 DEBUG: tools length: {len(tools) if hasattr(tools, '__len__') else 'no length'}")
            
            if tools and len(tools) > 0:
                await self._debug("info", f"🔍 DEBUG: first tool type: {type(tools[0])}")
                await self._debug("info", f"🔍 DEBUG: first tool repr: {repr(tools[0])}")
                if hasattr(tools[0], '__dict__'):
                    await self._debug("info", f"🔍 DEBUG: first tool dict: {tools[0].__dict__}")
                await self._debug("info", f"🔍 DEBUG: first tool dir: {dir(tools[0])}")

            # Handle both dict and object formats for tools
            tool_names = []
            for tool in tools:
                if hasattr(tool, 'name'):
                    tool_names.append(tool.name)
                elif isinstance(tool, dict) and 'name' in tool:
                    tool_names.append(tool['name'])
                else:
                    tool_names.append(str(tool))

            await self._debug("info", f"Connected to server with tools: {', '.join(tool_names)}", tools)

            # Prepare tools in the format expected by Anthropic API
            self.available_tools = []
            for tool in tools:
                if hasattr(tool, 'name'):
                    # Object format - debug the attributes
                    await self._debug("info", f"Tool object attributes: {dir(tool)}")
                    await self._debug("info", f"Tool type: {type(tool)}")
                    
                    # Handle different possible attribute names
                    description = getattr(tool, 'description', None) or getattr(tool, 'desc', '') or f"Tool: {tool.name}"
                    input_schema = getattr(tool, 'inputSchema', None) or getattr(tool, 'input_schema', {})
                    
                    self.available_tools.append({
                        "name": tool.name,
                        "description": description,
                        "input_schema": input_schema
                    })
                elif isinstance(tool, dict):
                    # Dict format
                    self.available_tools.append({
                        "name": tool.get('name', 'unknown'),
                        "description": tool.get('description', ''),
                        "input_schema": tool.get('inputSchema', {})
                    })

            self.connected = True
            return True

        except ConnectionRefusedError as e:
            await self._debug("error", f"Connection Error: Could not connect to the server at {server_url}.", e)
            await self._debug("error", "Please ensure the MCP server process is running, accessible, and listening on the correct address and port with streamable HTTP enabled.")
            self.connected = False
            return False

        except Exception as e:
            await self._debug("error", f"An unexpected error occurred during connection or initialization: {e}", e)
            self.connected = False
            return False

    async def call_tool(self,
                        tool_name: str,
                        tool_input: Any,
                        tool_call_id: str = "manual_call",
                        timeout: Optional[float] = None) -> ToolCallResult:
        """Call a tool via the MCP session with optional timeout.

        Args:
            tool_name: Name of the tool to call
            tool_input: Input for the tool
            tool_call_id: ID for the tool call (for tracking)
            timeout: Optional timeout in seconds

        Returns:
            ToolCallResult: Object containing the result and metadata
        """
        if not self.session:
            return ToolCallResult(
                tool_name=tool_name,
                tool_input=tool_input,
                tool_call_id=tool_call_id,
                success=False,
                error="Not connected to MCP server"
            )

        # Ensure tool_input is properly formatted as a dictionary
        formatted_input = tool_input

        # Handle echostring tool specifically - Level 0034: Fix parameter name
        if tool_name == "echostring":
            # Case 1: If input is a string, convert to {"text": string}
            if isinstance(tool_input, str):
                formatted_input = {"text": tool_input}
                await self._debug("info", f"Converted string input to dictionary: {formatted_input}")
            # Case 2: If input is a dict without "text" key but with other keys
            elif isinstance(tool_input, dict) and "text" not in tool_input and len(tool_input) > 0:
                # Use the first value as the text
                first_key = next(iter(tool_input))
                formatted_input = {"text": tool_input[first_key]}
                await self._debug("info", f"Extracted text from dictionary: {formatted_input}")

        await self._debug("info", f"Calling tool: '{tool_name}' with input: {formatted_input}")

        import time
        start_time = time.time()

        try:
            # Call the tool with timeout if specified
            if timeout:
                task = asyncio.create_task(self.session.call_tool(tool_name, formatted_input))
                try:
                    mcp_result = await asyncio.wait_for(task, timeout=timeout)
                except asyncio.TimeoutError:
                    task.cancel()
                    execution_time = time.time() - start_time
                    return ToolCallResult(
                        tool_name=tool_name,
                        tool_input=tool_input,  # Keep original input for reference
                        tool_call_id=tool_call_id,
                        success=False,
                        error=f"Tool call timed out after {timeout} seconds",
                        execution_time=execution_time
                    )
            else:
                mcp_result = await self.session.call_tool(tool_name, formatted_input)

            # 🔍 DEBUG: Raw MCP result inspection
            await self._debug("info", f"🔍 RAW MCP RESULT: type={type(mcp_result)}")
            await self._debug("info", f"🔍 RAW MCP RESULT: hasattr content={hasattr(mcp_result, 'content')}")
            if hasattr(mcp_result, 'content'):
                await self._debug("info", f"🔍 RAW MCP RESULT: content type={type(mcp_result.content)}")
                await self._debug("info", f"🔍 RAW MCP RESULT: content repr={repr(mcp_result.content)[:200]}")

            execution_time = time.time() - start_time

            # Process the result
            if mcp_result.isError:
                await self._debug("warning", f"Tool '{tool_name}' reported an error.", mcp_result)
                return ToolCallResult(
                    tool_name=tool_name,
                    tool_input=tool_input,
                    tool_call_id=tool_call_id,
                    success=False,
                    error=mcp_result.content if hasattr(mcp_result, 'content') else "Unknown error",
                    execution_time=execution_time
                )
            else:
                await self._debug("info", f"Tool '{tool_name}' executed successfully.", mcp_result)
                return ToolCallResult(
                    tool_name=tool_name,
                    tool_input=tool_input,
                    tool_call_id=tool_call_id,
                    success=True,
                    content=mcp_result.content if hasattr(mcp_result, 'content') else str(mcp_result),
                    execution_time=execution_time
                )

        except Exception as e:
            execution_time = time.time() - start_time
            await self._debug("error", f"Error calling tool '{tool_name}' via MCP: {e}", e)
            return ToolCallResult(
                tool_name=tool_name,
                tool_input=tool_input,
                tool_call_id=tool_call_id,
                success=False,
                error=f"Client-side error calling tool {tool_name}: {e}",
                execution_time=execution_time
            )

    async def process_query(self,
                           query: str,
                           model: str = DEFAULT_MODEL,
                           max_tokens: int = 1024,
                           tool_timeout: Optional[float] = None,
                           conversation_history: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Process a query using Claude and available tools via the MCP session.

        Args:
            query: The user query to process
            model: Claude model to use
            max_tokens: Maximum tokens for Claude response
            tool_timeout: Optional timeout for tool calls in seconds
            conversation_history: Optional conversation history to include

        Returns:
            Dict containing:
                - final_text: The final response text
                - messages: Updated conversation history
                - tool_results: List of tool call results
                - error: Error message if any
        """
        if not self.session:
            return {
                "final_text": "Error: Not connected to MCP server.",
                "messages": [],
                "tool_results": [],
                "error": "Not connected to MCP server"
            }

        # Check LLM client availability based on mode
        if not self.use_litellm and not self.anthropic:
            return {
                "final_text": "Error: Anthropic API client is not available. Please check your API key.",
                "messages": conversation_history.copy() if conversation_history else [],
                "tool_results": [],
                "error": "Anthropic API client is not available. Please check your API key."
            }
        elif self.use_litellm:
            # Check if LiteLLM is available, try to import if not
            global LITELLM_AVAILABLE, litellm
            if not LITELLM_AVAILABLE:
                try:
                    import litellm as test_litellm
                    await self._debug("info", "LiteLLM import successful on retry")
                    # Update the global flag since import succeeded
                    LITELLM_AVAILABLE = True
                    litellm = test_litellm
                except ImportError as e:
                    return {
                        "final_text": f"Error: LiteLLM is not available. Please install with: pip install litellm. Import error: {e}",
                        "messages": conversation_history.copy() if conversation_history else [],
                        "tool_results": [],
                        "error": f"LiteLLM is not available: {e}"
                    }

        # Initialize or use provided conversation history
        all_messages = conversation_history.copy() if conversation_history else []

        # Extract system messages and filter them out from the messages array
        system_messages = []
        messages = []
        for msg in all_messages:
            if msg.get("role") == "system":
                system_messages.append(msg["content"])
            else:
                messages.append(msg)

        # Combine system messages into a single system prompt
        system_prompt = "\n".join(system_messages) if system_messages else None

        # Add the user query if not already in history
        if not messages or messages[-1]["role"] != "user" or messages[-1]["content"] != query:
            messages.append({"role": "user", "content": query})

        final_response_parts = []  # Store parts of the final response text
        tool_results = []  # Store tool call results

        await self._debug("info", "Starting query processing", {"query": query, "messages": messages})

        try:
            # Initial call to LLM (Claude or other via LiteLLM)
            await self._debug("debug", f"Calling LLM API with tools (use_litellm={self.use_litellm})", self.available_tools)

            if self.use_litellm:
                # Use LiteLLM for unified model access
                # Convert tools to appropriate format based on model provider
                converted_tools = self._convert_tools_for_litellm(self.available_tools, model)

                api_params = {
                    "model": model,
                    "max_tokens": max_tokens,
                    "messages": messages,
                    "tools": converted_tools
                }

                # Add system prompt if available
                if system_prompt:
                    api_params["system"] = system_prompt
                    await self._debug("debug", f"Using system prompt: {system_prompt[:100]}...")

                response = litellm.completion(**api_params)
            else:
                # Legacy Anthropic-only mode
                api_params = {
                    "model": model,
                    "max_tokens": max_tokens,
                    "messages": messages,
                    "tools": self.available_tools
                }

                # Add system prompt if available
                if system_prompt:
                    api_params["system"] = system_prompt
                    await self._debug("debug", f"Using system prompt: {system_prompt[:100]}...")

                response = self.anthropic.messages.create(**api_params)

            await self._debug("debug", "Received initial LLM response", response)

            # Normalize response format
            normalized_response = self._normalize_response(response)

            # Append initial assistant message(s) to conversation history
            if self.use_litellm:
                # For LiteLLM, we need to handle the original response format
                # Check if this was an OpenAI model by looking at the model name
                if model.startswith(('gpt-', 'o1-')) or 'openai' in model.lower():
                    # For OpenAI models, use the original LiteLLM response format
                    choice = response.choices[0]
                    message = choice.message

                    assistant_message = {
                        "role": "assistant",
                        "content": message.content
                    }

                    # Add tool_calls if present
                    if hasattr(message, 'tool_calls') and message.tool_calls:
                        assistant_message["tool_calls"] = []
                        for tool_call in message.tool_calls:
                            assistant_message["tool_calls"].append({
                                "id": tool_call.id,
                                "type": "function",
                                "function": {
                                    "name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments
                                }
                            })

                    messages.append(assistant_message)

                    # Add text content to final response
                    if message.content:
                        final_response_parts.append(message.content)
                else:
                    # For Anthropic models via LiteLLM, use normalized format
                    assistant_content_blocks = []
                    for content_block in normalized_response.content:
                        if content_block.type == 'text':
                            final_response_parts.append(content_block.text)
                            assistant_content_blocks.append({"type": "text", "text": content_block.text})
                        elif content_block.type == 'tool_use':
                            assistant_content_blocks.append({
                                "type": "tool_use",
                                "id": content_block.id,
                                "name": content_block.name,
                                "input": content_block.input,
                            })

                    if assistant_content_blocks:
                        messages.append({"role": "assistant", "content": assistant_content_blocks})
            else:
                # Legacy Anthropic-only mode
                assistant_content_blocks = []
                for content_block in normalized_response.content:
                    if content_block.type == 'text':
                        final_response_parts.append(content_block.text)
                        assistant_content_blocks.append({"type": "text", "text": content_block.text})
                    elif content_block.type == 'tool_use':
                        assistant_content_blocks.append({
                            "type": "tool_use",
                            "id": content_block.id,
                            "name": content_block.name,
                            "input": content_block.input,
                        })

                if assistant_content_blocks:
                    messages.append({"role": "assistant", "content": assistant_content_blocks})

            # Process tool calls if any were requested
            tool_use_blocks = [block for block in normalized_response.content if block.type == 'tool_use']

            if tool_use_blocks:
                await self._debug("info", f"Claude requested {len(tool_use_blocks)} tool call(s).")
                tool_results_content = []  # Content for the next user message to Claude

                for tool_use in tool_use_blocks:
                    tool_name = tool_use.name
                    tool_input = tool_use.input
                    tool_call_id = tool_use.id  # Get the ID for the result

                    await self._debug("info", f"Processing tool call: '{tool_name}'", tool_use)
                    # Add placeholder text to final response
                    final_response_parts.append(f"\n[Calling tool '{tool_name}'...]")

                    # Execute the tool call via MCP session
                    tool_result = await self.call_tool(
                        tool_name=tool_name,
                        tool_input=tool_input,
                        tool_call_id=tool_call_id,
                        timeout=tool_timeout
                    )

                    # Store the tool result for return
                    tool_results.append(tool_result)

                    # Convert to appropriate tool result format based on mode and provider
                    if self.use_litellm and (model.startswith(('gpt-', 'o1-')) or 'openai' in model.lower()):
                        # For OpenAI models via LiteLLM, create tool result message
                        tool_result_content = tool_result.content or (tool_result.error if not tool_result.success else "")
                        tool_results_content.append({
                            "tool_call_id": tool_call_id,
                            "role": "tool",
                            "content": tool_result_content
                        })
                    elif self.use_litellm:
                        # For other LiteLLM providers (Anthropic via LiteLLM), use simple text
                        tool_result_content = tool_result.content or (tool_result.error if not tool_result.success else "")
                        tool_results_content.append(tool_result_content)
                    else:
                        # For direct Anthropic API, use tool_result block format
                        tool_result_block = tool_result.to_tool_result_block()
                        tool_results_content.append(tool_result_block)

                    # Update final response based on tool result
                    if not tool_result.success:
                        final_response_parts.append(f"[Failed to call tool '{tool_name}'.]")
                    else:
                        final_response_parts.append(f"\n")

                await self._debug("debug", "Tool results content", tool_results_content)

                # Send tool results back to LLM with appropriate format
                if self.use_litellm and (model.startswith(('gpt-', 'o1-')) or 'openai' in model.lower()):
                    # For OpenAI models, add tool result messages directly
                    for tool_result_msg in tool_results_content:
                        messages.append(tool_result_msg)
                elif self.use_litellm:
                    # For other LiteLLM providers, use simple text content
                    tool_results_text = "\n".join(str(content) for content in tool_results_content)
                    messages.append({
                        "role": "user",
                        "content": tool_results_text
                    })
                else:
                    # For direct Anthropic API, use structured content
                    messages.append({
                        "role": "user",
                        "content": tool_results_content
                    })

                # Get Claude's response summarizing tool results
                await self._debug("debug", "Calling Claude API with tool results")

                # Prepare follow-up API call parameters
                follow_up_params = {
                    "model": model,
                    "max_tokens": max_tokens,
                    "messages": messages,
                    # No tools needed for this follow-up
                }

                # Add system prompt if available
                if system_prompt:
                    follow_up_params["system"] = system_prompt

                # Use appropriate API based on mode
                if self.use_litellm:
                    follow_up_response = litellm.completion(**follow_up_params)
                else:
                    follow_up_response = self.anthropic.messages.create(**follow_up_params)

                await self._debug("debug", "Received follow-up response", follow_up_response)

                # Normalize follow-up response and add final text response
                normalized_follow_up = self._normalize_response(follow_up_response)
                for content_block in normalized_follow_up.content:
                    if content_block.type == 'text':
                        final_response_parts.append(content_block.text)

            # Join all collected response parts
            final_text = "\n".join(final_response_parts).strip()

            return {
                "final_text": final_text,
                "messages": messages,
                "tool_results": tool_results,
                "error": None
            }

        except Exception as e:
            await self._debug("error", f"Error during query processing: {e}", e)
            return {
                "final_text": f"An error occurred: {e}",
                "messages": messages,
                "tool_results": tool_results,
                "error": str(e)
            }

    async def cleanup(self):
        """Clean up resources with improved error handling."""
        await self._debug("info", "Cleaning up resources...")

        # Mark as disconnected first to prevent new operations
        self.connected = False

        try:
            # Try graceful cleanup with timeout
            await asyncio.wait_for(self.exit_stack.aclose(), timeout=2.0)
            await self._debug("info", "Cleanup complete.")
        except asyncio.TimeoutError:
            await self._debug("info", "Cleanup timeout - resources may not be fully cleaned")
        except Exception as e:
            await self._debug("info", f"Cleanup warning: {e} - continuing anyway")

        # Clear session reference if it exists
        if hasattr(self, 'session'):
            self.session = None


# Example usage in a command-line client
if __name__ == "__main__":
    import sys

    def debug_callback(level, message, data=None):
        """Simple debug callback that prints to console."""
        print(f"[{level.upper()}] {message}")
        if data and level == "debug":
            print(f"  Data: {data}")
        # Return None to indicate this is not a coroutine
        return None

    async def main():
        # Determine server URL: use command-line arg or default
        server_url = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_SERVER_URL

        # Create client with debug callback
        client = MCPClientLib(debug_callback=debug_callback)

        try:
            # Connect to server
            success = await client.connect_to_server(server_url)
            if not success:
                print("Failed to connect to server. Exiting.")
                return

            # Simple chat loop
            print("\nMCP Client Library Test")
            print("Type your queries or 'quit' to exit.")

            while True:
                query = input("\nQuery: ").strip()

                if not query:
                    continue
                if query.lower() == 'quit':
                    print("Exiting chat loop.")
                    break

                # Process the query
                result = await client.process_query(
                    query=query,
                    model=DEFAULT_MODEL,
                    max_tokens=1024,
                    tool_timeout=3600  # long timeout for long-running tools
                )

                # Print the result
                if result["error"]:
                    print(f"Error: {result['error']}")
                else:
                    print(f"\nResponse: {result['final_text']}")

        finally:
            # Ensure cleanup runs
            await client.cleanup()
            print("Cleanup complete.")

    # Run the main function
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
